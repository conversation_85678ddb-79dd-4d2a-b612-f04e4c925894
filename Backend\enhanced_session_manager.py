#!/usr/bin/env python3
"""
Enhanced Session Manager using LangGraph BedrockSessionSaver
Combines our custom AWS context management with LangGraph's robust checkpointing
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

import boto3
from botocore.exceptions import ClientError
from langgraph_checkpoint_aws.saver import BedrockSessionSaver
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage

import logging
logger = logging.getLogger(__name__)

@dataclass
class AWSCostOptimizationState:
    """State for AWS cost optimization workflows"""
    messages: List[Any]
    current_query: str
    aws_context: Dict[str, Any]
    cost_analysis_results: Dict[str, Any]
    optimization_recommendations: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    session_metadata: Dict[str, Any]

class EnhancedBedrockSessionManager:
    """
    Enhanced session manager that combines:
    1. LangGraph BedrockSessionSaver for robust checkpointing
    2. Custom AWS context management for cost optimization
    3. Workflow-based conversation handling
    """
    
    def __init__(self, config):
        self.config = config
        self.bedrock_saver = None
        self.fallback_sessions = {}
        self.session_enabled = False
        
        # Initialize BedrockSessionSaver
        if config.enable_bedrock_sessions:
            try:
                self.bedrock_saver = BedrockSessionSaver(
                    region_name=config.region,
                    credentials_profile_name=config.aws_profile if config.aws_profile != "default" else None
                )
                self.session_enabled = True
                logger.info("Enhanced Bedrock session management enabled with LangGraph")
            except Exception as e:
                logger.warning(f"Failed to initialize BedrockSessionSaver: {e}")
                self.session_enabled = False
        
        # Initialize workflow graph
        self.workflow_graph = self._create_cost_optimization_workflow()
    
    def _create_cost_optimization_workflow(self):
        """Create LangGraph workflow for cost optimization conversations"""
        
        def process_user_query(state: AWSCostOptimizationState) -> Dict:
            """Process user query with AWS context awareness"""
            # Extract AWS context from session
            aws_context = state.aws_context
            current_query = state.current_query
            
            # Add user message
            messages = list(state.messages)
            messages.append(HumanMessage(content=current_query))
            
            # Enhanced processing with AWS context
            enhanced_query = self._enhance_query_with_aws_context(current_query, aws_context)
            
            return {
                "messages": messages,
                "current_query": enhanced_query,
                "aws_context": self._update_aws_context(aws_context, current_query),
                "session_metadata": {
                    **state.session_metadata,
                    "last_query_time": datetime.now().isoformat(),
                    "query_count": state.session_metadata.get("query_count", 0) + 1
                }
            }
        
        def analyze_costs(state: AWSCostOptimizationState) -> Dict:
            """Perform cost analysis based on query"""
            # This would integrate with your existing tool execution
            cost_analysis = {
                "query_type": self._classify_cost_query(state.current_query),
                "relevant_services": self._extract_aws_services(state.current_query),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            return {
                **state,
                "cost_analysis_results": cost_analysis
            }
        
        def generate_recommendations(state: AWSCostOptimizationState) -> Dict:
            """Generate cost optimization recommendations"""
            recommendations = self._generate_cost_recommendations(
                state.cost_analysis_results,
                state.aws_context,
                state.user_preferences
            )
            
            # Add AI response
            messages = list(state.messages)
            response_content = self._format_recommendations_response(recommendations)
            messages.append(AIMessage(content=response_content))
            
            return {
                **state,
                "messages": messages,
                "optimization_recommendations": recommendations
            }
        
        def should_continue(state: AWSCostOptimizationState) -> str:
            """Determine next step in workflow"""
            if not state.current_query or state.current_query.lower() in ['quit', 'exit', 'done']:
                return "end"
            elif "cost" in state.current_query.lower() or "pricing" in state.current_query.lower():
                return "analyze_costs"
            else:
                return "process_query"
        
        # Build workflow graph
        workflow = StateGraph(AWSCostOptimizationState)
        
        # Add nodes
        workflow.add_node("process_query", process_user_query)
        workflow.add_node("analyze_costs", analyze_costs)
        workflow.add_node("generate_recommendations", generate_recommendations)
        
        # Add edges
        workflow.add_conditional_edges(
            "process_query",
            should_continue,
            {
                "analyze_costs": "analyze_costs",
                "process_query": "generate_recommendations",
                "end": END
            }
        )
        
        workflow.add_edge("analyze_costs", "generate_recommendations")
        workflow.add_edge("generate_recommendations", END)
        
        # Set entry point
        workflow.set_entry_point("process_query")
        
        return workflow.compile(checkpointer=self.bedrock_saver if self.session_enabled else None)
    
    async def create_enhanced_session(self, session_id: str, initial_context: Optional[Dict] = None) -> Dict:
        """Create enhanced session with workflow support"""
        aws_context = {
            "awsRegion": self.config.region,
            "awsProfile": self.config.aws_profile,
            "activeServices": [],
            "costOptimizationHistory": [],
            "userPreferences": initial_context or {},
            "createdAt": datetime.now().isoformat()
        }
        
        session_state = AWSCostOptimizationState(
            messages=[],
            current_query="",
            aws_context=aws_context,
            cost_analysis_results={},
            optimization_recommendations=[],
            user_preferences=initial_context or {},
            session_metadata={
                "session_id": session_id,
                "created_at": datetime.now().isoformat(),
                "query_count": 0
            }
        )
        
        if self.session_enabled:
            try:
                # LangGraph will handle session creation automatically
                logger.info(f"Enhanced session created with LangGraph: {session_id}")
                return {"session_id": session_id, "state": session_state}
            except Exception as e:
                logger.warning(f"Failed to create enhanced session: {e}")
                # Fallback to simple storage
                self.fallback_sessions[session_id] = session_state
                return {"session_id": session_id, "state": session_state, "fallback": True}
        else:
            self.fallback_sessions[session_id] = session_state
            return {"session_id": session_id, "state": session_state, "fallback": True}
    
    async def process_conversation_turn(self, session_id: str, user_message: str) -> Dict:
        """Process a conversation turn using the workflow"""
        try:
            if self.session_enabled:
                # Use LangGraph workflow with automatic checkpointing
                config = {"configurable": {"thread_id": session_id}}
                
                # Get current state or create initial state
                try:
                    current_state = self.workflow_graph.get_state(config)
                    if current_state.values:
                        state = current_state.values
                    else:
                        # Create initial state
                        session_data = await self.create_enhanced_session(session_id)
                        state = session_data["state"]
                except:
                    session_data = await self.create_enhanced_session(session_id)
                    state = session_data["state"]
                
                # Update state with new query
                state.current_query = user_message
                
                # Process through workflow
                result = self.workflow_graph.invoke(state, config)
                
                return {
                    "response": self._extract_response_from_messages(result.get("messages", [])),
                    "aws_context": result.get("aws_context", {}),
                    "recommendations": result.get("optimization_recommendations", []),
                    "session_id": session_id
                }
            else:
                # Fallback processing
                return await self._process_fallback_conversation(session_id, user_message)
                
        except Exception as e:
            logger.error(f"Error processing conversation turn: {e}")
            return await self._process_fallback_conversation(session_id, user_message)
    
    def _enhance_query_with_aws_context(self, query: str, aws_context: Dict) -> str:
        """Enhance user query with AWS context"""
        context_info = []
        
        if aws_context.get("awsRegion"):
            context_info.append(f"Region: {aws_context['awsRegion']}")
        
        if aws_context.get("activeServices"):
            services = ", ".join(aws_context["activeServices"])
            context_info.append(f"Active services: {services}")
        
        if context_info:
            enhanced_query = f"{query}\n\nContext: {'; '.join(context_info)}"
            return enhanced_query
        
        return query
    
    def _update_aws_context(self, aws_context: Dict, query: str) -> Dict:
        """Update AWS context based on query"""
        updated_context = aws_context.copy()
        
        # Extract services mentioned in query
        services = self._extract_aws_services(query)
        if services:
            current_services = set(updated_context.get("activeServices", []))
            current_services.update(services)
            updated_context["activeServices"] = list(current_services)
        
        updated_context["lastQueryTime"] = datetime.now().isoformat()
        return updated_context
    
    def _classify_cost_query(self, query: str) -> str:
        """Classify the type of cost query"""
        query_lower = query.lower()
        
        if "pricing" in query_lower:
            return "pricing_inquiry"
        elif "cost" in query_lower and "optimization" in query_lower:
            return "cost_optimization"
        elif "savings" in query_lower:
            return "savings_analysis"
        elif "budget" in query_lower:
            return "budget_planning"
        else:
            return "general_inquiry"
    
    def _extract_aws_services(self, query: str) -> List[str]:
        """Extract AWS services mentioned in query"""
        services = []
        query_lower = query.lower()
        
        service_keywords = {
            "ec2": "EC2",
            "s3": "S3",
            "rds": "RDS",
            "lambda": "Lambda",
            "cloudfront": "CloudFront",
            "elb": "ELB",
            "ebs": "EBS"
        }
        
        for keyword, service in service_keywords.items():
            if keyword in query_lower:
                services.append(service)
        
        return services
    
    def _generate_cost_recommendations(self, analysis: Dict, aws_context: Dict, preferences: Dict) -> List[Dict]:
        """Generate cost optimization recommendations"""
        # This would integrate with your existing tool execution logic
        recommendations = [
            {
                "type": "cost_optimization",
                "service": "EC2",
                "recommendation": "Consider using Reserved Instances for long-running workloads",
                "potential_savings": "Up to 75%",
                "priority": "high"
            }
        ]
        return recommendations
    
    def _format_recommendations_response(self, recommendations: List[Dict]) -> str:
        """Format recommendations into response text"""
        if not recommendations:
            return "I don't have specific recommendations at this time. Please provide more details about your AWS usage."
        
        response_parts = ["Based on your query, here are my cost optimization recommendations:\n"]
        
        for i, rec in enumerate(recommendations, 1):
            response_parts.append(
                f"{i}. {rec.get('recommendation', 'No recommendation available')}\n"
                f"   Service: {rec.get('service', 'N/A')}\n"
                f"   Potential Savings: {rec.get('potential_savings', 'N/A')}\n"
            )
        
        return "\n".join(response_parts)
    
    def _extract_response_from_messages(self, messages: List) -> str:
        """Extract the latest AI response from messages"""
        for message in reversed(messages):
            if isinstance(message, AIMessage):
                return message.content
        return "I apologize, but I couldn't generate a response. Please try again."
    
    async def _process_fallback_conversation(self, session_id: str, user_message: str) -> Dict:
        """Fallback conversation processing"""
        return {
            "response": f"Processed message: {user_message} (using fallback mode)",
            "aws_context": {},
            "recommendations": [],
            "session_id": session_id
        }
    
    async def get_session_history(self, session_id: str) -> Dict:
        """Get session history with workflow state"""
        if self.session_enabled:
            try:
                config = {"configurable": {"thread_id": session_id}}
                state_history = list(self.workflow_graph.get_state_history(config, limit=10))
                
                return {
                    "session_id": session_id,
                    "history": state_history,
                    "source": "langgraph_bedrock"
                }
            except Exception as e:
                logger.warning(f"Failed to get LangGraph history: {e}")
        
        # Fallback
        if session_id in self.fallback_sessions:
            return {
                "session_id": session_id,
                "history": [self.fallback_sessions[session_id]],
                "source": "fallback"
            }
        
        return {"session_id": session_id, "history": [], "source": "not_found"}
