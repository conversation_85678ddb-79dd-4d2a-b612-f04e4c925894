import shutil
import os
import json
import asyncio
import uuid
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
from contextlib import AsyncExitStack
from mcp.client.stdio import stdio_client
import boto3
from botocore.exceptions import Client<PERSON>rror, NoCredentialsError
from mcp import ClientSession, StdioServerParameters
import logging
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
import uvicorn
from datetime import datetime, timedelta
import threading

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI app with lifespan
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    global chat_session, is_initialized

    with initialization_lock:
        if is_initialized:
            yield
            return

        try:
            logger.info("Starting application initialization...")

            config = Configuration()

            if not config.model_id:
                raise ValueError("BEDROCK_MODEL_ID environment variable is required")

            # Test AWS credentials
            try:
                if config.aws_profile == "default":
                    session = boto3.Session()
                else:
                    session = boto3.Session(profile_name=config.aws_profile)

                # Test access
                sts = session.client('sts')
                identity = sts.get_caller_identity()
                logger.info(f"AWS identity confirmed: {identity.get('Arn', 'Unknown')}")

            except Exception as e:
                logger.error(f"AWS credential test failed: {e}")
                raise ValueError(f"AWS credentials not properly configured: {e}")

            # Initialize Bedrock client
            bedrock = BedrockClient(
                model_id=config.model_id,
                region=config.region,
                aws_profile=config.aws_profile
            )

            # Initialize chat session
            chat_session = ChatSession(bedrock, config)
            await chat_session.initialize()

            is_initialized = True
            logger.info("Application initialized successfully")

        except Exception as e:
            logger.error(f"Error during startup: {e}")
            is_initialized = False
            raise

    yield

    # Shutdown
    logger.info("Application shutdown initiated")
    if chat_session:
        try:
            await chat_session.cleanup()
        except Exception as e:
            logger.warning(f"Error cleaning up chat session: {e}")
    logger.info("Application shutdown completed")

app = FastAPI(
    title="AWS Pricing Optimization Agent API",
    version="1.0.0",
    description="AI-powered AWS cost optimization assistant with automatic tool execution",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
chat_session = None
is_initialized = False
initialization_lock = threading.Lock()

# ------------------------
# Configuration
# ------------------------
class Configuration:
    def __init__(self):
        load_dotenv()
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-sonnet-20240229-v1:0")
        self.region = os.getenv("AWS_REGION", "ap-south-1")
        self.aws_profile = os.getenv("AWS_PROFILE", "genai")
        self.max_tokens = int(os.getenv("MAX_TOKENS", "4000"))
        self.temperature = float(os.getenv("TEMPERATURE", "0.1"))

        # Bedrock Session Management Configuration
        self.enable_bedrock_sessions = os.getenv("ENABLE_BEDROCK_SESSIONS", "true").lower() == "true"
        self.bedrock_session_fallback = os.getenv("BEDROCK_SESSION_FALLBACK", "true").lower() == "true"
        self.session_retention_days = int(os.getenv("SESSION_RETENTION_DAYS", "7"))

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(file_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Configuration file {file_path} not found, creating default")
            return Configuration.create_default_config(file_path)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing {file_path}: {e}")
            raise

    @staticmethod
    def create_default_config(file_path: str) -> dict[str, Any]:
        """Create default configuration file"""
        default_config = {
            "mcpServers": {
                "aws-pricing": {
                    "command": "npx",
                    "args": ["@modelcontextprotocol/server-aws-pricing"],
                    "env": {},
                    "description": "AWS Pricing API server for cost analysis"
                },
                "calculator": {
                    "command": "npx",
                    "args": ["@modelcontextprotocol/server-calculator"],
                    "env": {},
                    "description": "Calculator server for cost calculations"
                }
            }
        }
        
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w") as f:
                json.dump(default_config, f, indent=2)
            logger.info(f"Created default configuration at {file_path}")
        except Exception as e:
            logger.error(f"Failed to create default config: {e}")
            
        return default_config

# ------------------------
# Pydantic Models
# ------------------------
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    tool_executions: List[Dict[str, Any]] = []
    timestamp: str
    tokens_used: Optional[int] = None
    processing_time: float

class ErrorResponse(BaseModel):
    error: str
    details: Optional[str] = None
    timestamp: str

# ------------------------
# Tool Class
# ------------------------
class Tool:
    def __init__(self, name: str, description: str, input_schema: dict[str, Any], server_name: str):
        self.name = name
        self.description = description[:500]  # Limit description length
        self.input_schema = input_schema
        self.server_name = server_name

    def to_bedrock_format(self) -> Dict:
        """Convert tool to Bedrock-compatible format"""
        return {
            "toolSpec": {
                "name": self.name,
                "description": self.description,
                "inputSchema": {
                    "json": {
                        "type": "object",
                        "properties": self.input_schema.get("properties", {}),
                        "required": self.input_schema.get("required", [])
                    }
                },
            }
        }

# ------------------------
# Server Class
# ------------------------
@dataclass
class Server:
    def __init__(self, name: str, config: dict[str, Any]):
        self.name = name
        self.config = config
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self._initialized = False
        self._max_retries = 3

    async def initialize(self):
        """Initialize MCP server with retry logic"""
        if self._initialized:
            return
            
        for attempt in range(self._max_retries):
            try:
                await self._attempt_initialization()
                self._initialized = True
                logger.info(f"Successfully initialized server: {self.name}")
                return
                
            except Exception as e:
                logger.warning(f"Initialization attempt {attempt + 1} failed for {self.name}: {e}")
                
                if attempt == self._max_retries - 1:
                    logger.error(f"Failed to initialize server {self.name} after {self._max_retries} attempts")
                    await self.cleanup()
                    raise
                
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

    async def _attempt_initialization(self):
        """Single initialization attempt"""
        command = self.config.get("command")
        if command == "npx":
            command = shutil.which("npx")
            if command is None:
                raise ValueError("npx command not found in PATH")

        # Get the working directory from config, resolve relative paths
        cwd = self.config.get("cwd")
        if cwd and not os.path.isabs(cwd):
            # Resolve relative path from the backend directory
            cwd = os.path.abspath(os.path.join(os.path.dirname(__file__), cwd))

        server_params = StdioServerParameters(
            command=command,
            args=self.config.get("args", []),
            env={**os.environ, **self.config.get("env", {})},
            cwd=cwd
        )
        
        stdio_transport = await asyncio.wait_for(
            self.exit_stack.enter_async_context(stdio_client(server_params)),
            timeout=30.0
        )
        
        read, write = stdio_transport
        session = await asyncio.wait_for(
            self.exit_stack.enter_async_context(ClientSession(read, write)),
            timeout=30.0
        )
        
        await asyncio.wait_for(session.initialize(), timeout=30.0)
        self.session = session

    async def list_tools(self) -> List[Tool]:
        """List available tools from this server"""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            tools_response = await asyncio.wait_for(self.session.list_tools(), timeout=15.0)
            tools = []

            # Handle different response formats
            if hasattr(tools_response, 'tools'):
                tools_list = tools_response.tools
            elif isinstance(tools_response, list):
                tools_list = tools_response
            else:
                tools_list = []
                for item in tools_response:
                    if isinstance(item, tuple) and len(item) == 2 and item[0] == "tools":
                        tools_list.extend(item[1])

            for tool in tools_list:
                tools.append(Tool(
                    name=tool.name,
                    description=tool.description,
                    input_schema=tool.inputSchema,
                    server_name=self.name
                ))

            logger.info(f"Listed {len(tools)} tools from server {self.name}")
            return tools
            
        except Exception as e:
            logger.error(f"Error listing tools for server {self.name}: {e}")
            return []

    async def execute_tool(self, tool_name: str, arguments: dict[str, Any]) -> Any:
        """Execute a tool with given arguments"""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            logger.info(f"Executing tool {tool_name} with args: {arguments}")
            result = await asyncio.wait_for(
                self.session.call_tool(tool_name, arguments),
                timeout=60.0
            )
            logger.info(f"Tool {tool_name} executed successfully")
            return result
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            raise

    async def cleanup(self):
        """Clean up server resources"""
        try:
            if self._initialized:
                self._initialized = False
                self.session = None
                await asyncio.wait_for(self.exit_stack.aclose(), timeout=5.0)
                logger.info(f"Server {self.name} cleaned up")
        except Exception as e:
            logger.warning(f"Cleanup error for {self.name}: {e}")

# ------------------------
# Bedrock Client with Complete Response
# ------------------------
class BedrockClient:
    def __init__(self, model_id: str, region: str, aws_profile: str):
        self.model_id = model_id
        self.region = region
        
        try:
            if aws_profile == "default":
                self.session = boto3.Session()
            else:
                self.session = boto3.Session(profile_name=aws_profile)
                
            self.bedrock_runtime = self.session.client("bedrock-runtime", region_name=region)
            logger.info(f"Initialized Bedrock client with model {model_id} in {region}")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {e}")
            raise

    async def converse_with_tools(
        self, 
        messages: List[Dict[str, Any]], 
        tools: List[Dict[str, Any]],
        tool_executor,
        max_tokens: int = 4000,
        temperature: float = 0.1,
        max_tool_iterations: int = 5
    ) -> tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """Converse with automatic tool execution and return complete response"""
        
        current_messages = messages.copy()
        iterations = 0
        tool_executions = []
        final_response = ""
        usage_info = {}
        
        while iterations < max_tool_iterations:
            try:
                response = self.bedrock_runtime.converse(
                    modelId=self.model_id,
                    messages=current_messages,
                    inferenceConfig={
                        "maxTokens": max_tokens, 
                        "temperature": temperature,
                    },
                    toolConfig={"tools": tools} if tools else {},
                )
                
                # Extract usage information
                if 'usage' in response:
                    usage_info = response['usage']
                
                # Process the response
                message = response['output']['message']
                content = message.get('content', [])
                
                # Check for text content
                text_content = ""
                tool_use_blocks = []
                
                for content_block in content:
                    if 'text' in content_block:
                        text_content += content_block['text']
                    elif 'toolUse' in content_block:
                        tool_use_blocks.append(content_block['toolUse'])
                
                # If there are tool uses, execute them
                if tool_use_blocks:
                    # Add assistant message with tool use to conversation
                    current_messages.append({
                        "role": "assistant",
                        "content": content
                    })
                    
                    tool_results_message = {"role": "user", "content": []}
                    
                    for tool_block in tool_use_blocks:
                        tool_execution = {
                            "tool_name": tool_block['name'],
                            "arguments": tool_block['input'],
                            "tool_use_id": tool_block['toolUseId'],
                            "status": "pending",
                            "result": None,
                            "error": None
                        }
                        
                        try:
                            logger.info(f"Executing tool: {tool_block['name']}")
                            result = await tool_executor(
                                tool_block['name'], 
                                tool_block['input']
                            )
                            
                            # Format result for Bedrock
                            if hasattr(result, 'content') and result.content:
                                if isinstance(result.content, list) and result.content:
                                    result_text = ""
                                    for content_item in result.content:
                                        if hasattr(content_item, 'text'):
                                            result_text += content_item.text
                                        else:
                                            result_text += str(content_item)
                                else:
                                    result_text = str(result.content)
                            else:
                                result_text = str(result)
                            
                            tool_execution["status"] = "success"
                            tool_execution["result"] = result_text
                            
                            tool_results_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_block['toolUseId'],
                                    "content": [{"text": result_text}]
                                }
                            })
                            
                        except Exception as e:
                            logger.error(f"Tool execution failed for {tool_block['name']}: {e}")
                            error_text = f"Error executing {tool_block['name']}: {str(e)}"
                            
                            tool_execution["status"] = "error"
                            tool_execution["error"] = str(e)
                            
                            tool_results_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_block['toolUseId'],
                                    "content": [{"text": error_text}],
                                    "status": "error"
                                }
                            })
                        
                        tool_executions.append(tool_execution)
                    
                    # Add tool results to conversation
                    current_messages.append(tool_results_message)
                    iterations += 1
                    
                    # Continue the conversation with tool results
                    continue
                    
                else:
                    # No tool use, this is the final response
                    final_response = text_content
                    
                    # Add the final assistant response to the original messages list
                    # This ensures the conversation history is updated with the final response
                    messages.append({
                        "role": "assistant",
                        "content": [{"text": final_response}]
                    })
                    
                    break
                    
            except Exception as e:
                logger.error(f"Error in Bedrock conversation iteration {iterations}: {e}")
                final_response = f"I apologize, but I encountered an error while processing your request: {str(e)}"
                
                # Add error response to conversation history
                messages.append({
                    "role": "assistant", 
                    "content": [{"text": final_response}]
                })
                
                break
        
        if iterations >= max_tool_iterations:
            final_response += f"\n\nNote: Maximum tool execution iterations ({max_tool_iterations}) reached."
        
        return final_response, tool_executions, usage_info

# ------------------------
# Bedrock Session Manager
# ------------------------
class BedrockSessionManager:
    """Manages AWS Bedrock sessions with fallback to in-memory storage"""

    def __init__(self, config: Configuration):
        self.config = config
        self.bedrock_agent_runtime = None
        self.fallback_sessions = {}  # In-memory fallback
        self.session_enabled = False

        if config.enable_bedrock_sessions:
            try:
                if config.aws_profile == "default":
                    session = boto3.Session()
                else:
                    session = boto3.Session(profile_name=config.aws_profile)

                # Use bedrock-agent-runtime as recommended by AWS docs
                self.bedrock_agent_runtime = session.client(
                    'bedrock-agent-runtime',
                    region_name=config.region
                )

                # Test connection with a simple call
                try:
                    # This will validate our credentials and permissions
                    test_session = self.bedrock_agent_runtime.create_session()
                    test_session_id = test_session.get('sessionId') or test_session.get('sessionIdentifier')
                    # Clean up test session - try both parameter names
                    try:
                        self.bedrock_agent_runtime.delete_session(sessionId=test_session_id)
                    except:
                        self.bedrock_agent_runtime.delete_session(sessionIdentifier=test_session_id)
                    logger.info("Bedrock session management enabled and tested successfully")
                except Exception as test_error:
                    logger.warning(f"Bedrock session test failed, but continuing: {test_error}")

                self.session_enabled = True
            except Exception as e:
                logger.warning(f"Failed to initialize Bedrock sessions, using fallback: {e}")
                self.session_enabled = False
        else:
            logger.info("Bedrock session management disabled, using in-memory storage")

    async def create_session(self, session_id: str, initial_context: Optional[Dict] = None) -> Dict:
        """Create a new session with AWS context"""
        session_data = {
            "sessionId": session_id,
            "sessionState": {
                "sessionAttributes": {
                    "awsRegion": self.config.region,
                    "awsProfile": self.config.aws_profile,
                    "createdAt": datetime.now().isoformat(),
                    "messageCount": 0,
                    "costOptimizationContext": {},
                    "activeServices": [],
                    "lastPricingQuery": None,
                    "budgetContext": {},
                    **(initial_context or {})
                }
            }
        }

        if self.session_enabled:
            try:
                # Use the AWS recommended approach: let Bedrock create the session ID
                # or use our provided session_id
                if hasattr(self.bedrock_agent_runtime, 'create_session'):
                    # Let Bedrock generate the session ID (AWS requirement)
                    response = self.bedrock_agent_runtime.create_session()

                    # Map our session_id to the Bedrock-generated one
                    bedrock_session_id = response.get('sessionId') or response.get('sessionIdentifier')
                    if bedrock_session_id:
                        # Store the mapping
                        self.fallback_sessions[f"{session_id}_bedrock_mapping"] = bedrock_session_id
                        # Store session data with our session_id for consistency
                        response['sessionId'] = session_id
                        response['bedrock_session_id'] = bedrock_session_id
                        logger.info(f"Mapped session {session_id} to Bedrock session {bedrock_session_id}")
                    else:
                        raise Exception("No session ID returned from Bedrock")
                else:
                    # If create_session doesn't exist, use fallback
                    raise Exception("create_session method not available")

                logger.info(f"Created Bedrock session: {session_id}")
                return response
            except Exception as e:
                logger.warning(f"Failed to create Bedrock session {session_id}, using fallback: {e}")
                if self.config.bedrock_session_fallback:
                    self.fallback_sessions[session_id] = session_data
                    return session_data
                else:
                    raise
        else:
            # Use fallback storage
            self.fallback_sessions[session_id] = session_data
            return session_data

    async def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session data"""
        if self.session_enabled:
            try:
                # Get the Bedrock session ID from mapping
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # Use the mapped Bedrock session ID
                    response = self.bedrock_agent_runtime.get_session(sessionIdentifier=bedrock_session_id)
                    # Add our session_id for consistency
                    response['sessionId'] = session_id
                    return response
                else:
                    # No mapping found, check fallback storage
                    return self.fallback_sessions.get(session_id)

            except ClientError as e:
                if e.response['Error']['Code'] == 'ResourceNotFoundException':
                    # Check fallback storage
                    return self.fallback_sessions.get(session_id)
                else:
                    logger.warning(f"Error getting Bedrock session {session_id}: {e}")
                    if self.config.bedrock_session_fallback:
                        return self.fallback_sessions.get(session_id)
                    else:
                        raise
            except Exception as e:
                logger.warning(f"Unexpected error getting session {session_id}: {e}")
                if self.config.bedrock_session_fallback:
                    return self.fallback_sessions.get(session_id)
                else:
                    raise
        else:
            return self.fallback_sessions.get(session_id)

    async def update_session(self, session_id: str, session_state: Dict) -> Dict:
        """Update session state"""
        if self.session_enabled:
            try:
                # Get the Bedrock session ID from mapping
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # Use the mapped Bedrock session ID
                    response = self.bedrock_agent_runtime.update_session(
                        sessionIdentifier=bedrock_session_id,
                        sessionState=session_state
                    )
                    response['sessionId'] = session_id  # Keep our session_id for consistency
                    return response
                else:
                    # No mapping found, update fallback storage
                    if session_id in self.fallback_sessions:
                        self.fallback_sessions[session_id]["sessionState"].update(session_state)
                    return self.fallback_sessions.get(session_id, {})

            except Exception as e:
                logger.warning(f"Failed to update Bedrock session {session_id}: {e}")
                if self.config.bedrock_session_fallback:
                    if session_id in self.fallback_sessions:
                        self.fallback_sessions[session_id]["sessionState"].update(session_state)
                    return self.fallback_sessions.get(session_id, {})
                else:
                    raise
        else:
            # Update fallback storage
            if session_id in self.fallback_sessions:
                self.fallback_sessions[session_id]["sessionState"].update(session_state)
            return self.fallback_sessions.get(session_id, {})

    async def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        success = True

        if self.session_enabled:
            try:
                # Get the Bedrock session ID from mapping
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # Use the mapped Bedrock session ID
                    self.bedrock_agent_runtime.delete_session(sessionIdentifier=bedrock_session_id)
                    logger.info(f"Deleted Bedrock session: {session_id} (mapped to {bedrock_session_id})")
                    # Remove the mapping
                    del self.fallback_sessions[f"{session_id}_bedrock_mapping"]

            except ClientError as e:
                if e.response['Error']['Code'] != 'ResourceNotFoundException':
                    logger.warning(f"Error deleting Bedrock session {session_id}: {e}")
                    success = False
            except Exception as e:
                logger.warning(f"Unexpected error deleting session {session_id}: {e}")
                success = False

        # Also remove from fallback storage
        if session_id in self.fallback_sessions:
            del self.fallback_sessions[session_id]

        return success

    async def create_invocation_checkpoint(self, session_id: str, step_data: Dict) -> str:
        """Create a checkpoint for conversation step"""
        invocation_id = str(uuid.uuid4())

        if self.session_enabled:
            try:
                # Create invocation
                self.bedrock_agent_runtime.create_invocation(
                    sessionId=session_id,
                    invocationId=invocation_id
                )

                # Store step data
                step_id = f"step_{int(time.time())}"
                self.bedrock_agent_runtime.put_invocation_step(
                    sessionId=session_id,
                    invocationId=invocation_id,
                    stepId=step_id,
                    stepInput=step_data
                )

                return invocation_id
            except Exception as e:
                logger.warning(f"Failed to create invocation checkpoint: {e}")
                # Store in fallback
                if session_id in self.fallback_sessions:
                    if "invocations" not in self.fallback_sessions[session_id]:
                        self.fallback_sessions[session_id]["invocations"] = []
                    self.fallback_sessions[session_id]["invocations"].append({
                        "invocationId": invocation_id,
                        "stepData": step_data,
                        "timestamp": datetime.now().isoformat()
                    })
                return invocation_id
        else:
            # Store in fallback
            if session_id in self.fallback_sessions:
                if "invocations" not in self.fallback_sessions[session_id]:
                    self.fallback_sessions[session_id]["invocations"] = []
                self.fallback_sessions[session_id]["invocations"].append({
                    "invocationId": invocation_id,
                    "stepData": step_data,
                    "timestamp": datetime.now().isoformat()
                })
            return invocation_id

# ------------------------
# Chat Session
# ------------------------
class ChatSession:
    def __init__(self, bedrock: BedrockClient, config: Configuration):
        self.bedrock = bedrock
        self.config = config
        self.servers: Dict[str, Server] = {}
        self.tools: List[Tool] = []
        self.tool_to_server: Dict[str, str] = {}

        # Initialize Bedrock Session Manager
        self.session_manager = BedrockSessionManager(config)

        # Keep legacy storage for backward compatibility
        self.conversations: Dict[str, List[Dict]] = {}
        self.session_metadata: Dict[str, Dict] = {}

    async def initialize(self):
        """Initialize chat session with all MCP servers"""
        try:
            # Load server configuration
            config_data = Configuration.load_config("./servers_config.json")
            
            # Initialize servers
            initialization_tasks = []
            for name, server_config in config_data["mcpServers"].items():
                server = Server(name, server_config)
                self.servers[name] = server
                initialization_tasks.append(server.initialize())
            
            # Wait for all servers to initialize (with some fault tolerance)
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            initialized_servers = []
            for i, (name, result) in enumerate(zip(config_data["mcpServers"].keys(), results)):
                if isinstance(result, Exception):
                    logger.error(f"Failed to initialize server {name}: {result}")
                else:
                    initialized_servers.append(name)
            
            if not initialized_servers:
                logger.warning("No servers initialized successfully")
            
            # Get all tools from successfully initialized servers
            all_tools = []
            for server_name in initialized_servers:
                server = self.servers[server_name]
                try:
                    tools = await server.list_tools()
                    all_tools.extend(tools)
                    # Build tool mapping
                    for tool in tools:
                        self.tool_to_server[tool.name] = server.name
                except Exception as e:
                    logger.error(f"Failed to list tools for server {server_name}: {e}")

            self.tools = all_tools
            logger.info(f"Chat session initialized with {len(all_tools)} tools from {len(initialized_servers)} servers")

        except Exception as e:
            logger.error(f"Failed to initialize chat session: {e}")
            raise

    async def get_or_create_session(self, session_id: str) -> Dict:
        """Get existing session or create new one with AWS context"""
        try:
            # Try to get existing session
            session_data = await self.session_manager.get_session(session_id)

            if session_data:
                logger.info(f"Retrieved existing session: {session_id}")
                return session_data
            else:
                # Create new session with AWS context
                initial_context = {
                    "costOptimizationContext": {},
                    "activeServices": [],
                    "lastPricingQuery": None,
                    "budgetContext": {}
                }

                session_data = await self.session_manager.create_session(session_id, initial_context)
                logger.info(f"Created new session: {session_id}")
                return session_data

        except Exception as e:
            logger.error(f"Error managing session {session_id}: {e}")
            # Fallback to legacy session creation
            if session_id not in self.conversations:
                self.conversations[session_id] = []
                self.session_metadata[session_id] = {
                    "created_at": datetime.now().isoformat(),
                    "message_count": 0
                }
            return {"sessionId": session_id, "fallback": True}

    async def update_session_context(self, session_id: str, updates: Dict) -> None:
        """Update session context with new information"""
        try:
            session_data = await self.session_manager.get_session(session_id)
            if session_data:
                current_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})
                current_attributes.update(updates)

                await self.session_manager.update_session(session_id, {
                    "sessionAttributes": current_attributes
                })
                logger.debug(f"Updated session context for {session_id}")
        except Exception as e:
            logger.warning(f"Failed to update session context for {session_id}: {e}")

    def _extract_aws_services_from_tools(self, tool_executions: List[Dict]) -> List[str]:
        """Extract AWS services from tool executions"""
        services = set()
        for execution in tool_executions:
            tool_name = execution.get('tool_name', '').lower()
            if 'ec2' in tool_name:
                services.add('EC2')
            elif 's3' in tool_name:
                services.add('S3')
            elif 'rds' in tool_name:
                services.add('RDS')
            elif 'lambda' in tool_name:
                services.add('Lambda')
            elif 'pricing' in tool_name:
                services.add('Pricing')
            elif 'cost' in tool_name:
                services.add('Cost Explorer')
        return list(services)

    def _check_cost_analysis(self, tool_executions: List[Dict]) -> bool:
        """Check if cost analysis was performed"""
        for execution in tool_executions:
            tool_name = execution.get('tool_name', '').lower()
            if 'cost' in tool_name or 'pricing' in tool_name:
                return True
        return False

    def _get_system_prompt(self) -> str:
        """Get system prompt with available tools"""
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description}")

        tools_text = "\n".join(tool_descriptions) if tool_descriptions else "No tools available"

        return f"""You are an AWS Cost Optimization Assistant with access to real-time AWS pricing data and calculation tools.

Your capabilities include:
{tools_text}

Guidelines:
1. Focus on answering the user's current question directly and comprehensively
2. Always use tools to provide accurate, up-to-date AWS pricing information
3. Offer specific, actionable cost optimization recommendations
4. Explain cost calculations clearly and show your work
5. Consider both immediate and long-term cost implications
6. Be comprehensive and detailed in your responses to the current question
7. When analyzing costs, consider factors like:
   - Instance types and sizes
   - Storage options and classes
   - Data transfer costs
   - Reserved instances vs on-demand vs spot pricing
   - Regional pricing differences
   - Multi-AZ deployments
8. Always verify current pricing with tools before making recommendations
9. Provide concrete savings estimates when possible
10. Format your responses clearly with sections and bullet points where appropriate
11. If the user asks a follow-up question, provide context from the conversation but focus on the new question

IMPORTANT: Answer only the user's current/latest question. Do not repeat or regenerate responses to previous questions in the conversation."""

    async def _get_aws_context_aware_system_prompt(self, session_id: str) -> str:
        """Get system prompt enhanced with AWS session context"""
        base_prompt = self._get_system_prompt()

        try:
            # Get session context
            session_data = await self.session_manager.get_session(session_id)
            if not session_data:
                return base_prompt

            session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})

            # Build context-aware additions
            aws_context = []

            if session_attributes.get('awsRegion'):
                aws_context.append(f"Current AWS Region: {session_attributes['awsRegion']}")

            if session_attributes.get('activeServices'):
                services = ', '.join(session_attributes['activeServices'])
                aws_context.append(f"Active AWS Services in this session: {services}")

            if session_attributes.get('lastPricingQuery'):
                last_query = session_attributes['lastPricingQuery']
                aws_context.append(f"Recent pricing analysis: {last_query.get('tool')} at {last_query.get('timestamp')}")

            if session_attributes.get('costOptimizationContext'):
                aws_context.append("Previous cost optimization analysis available in session context")

            if aws_context:
                context_text = "\n".join(aws_context)
                return f"""{base_prompt}

Session Context:
{context_text}

Use this context to provide more relevant and personalized recommendations."""

            return base_prompt

        except Exception as e:
            logger.warning(f"Failed to get session context for prompt: {e}")
            return base_prompt

    def _build_conversation_context(self, conversation: List[Dict], current_message: str) -> List[Dict]:
        """Build conversation context optimized for the current question"""

        # Start with system prompt
        context = [{
            "role": "user",
            "content": [{"text": self._get_system_prompt()}]
        }]

        # If there's conversation history, provide summarized context
        if len(conversation) > 1:  # More than just system message
            # Get the last few exchanges (exclude system message)
            recent_conversation = conversation[1:]  # Skip system message

            # Keep last 3 user-assistant pairs for context (max 6 messages)
            if len(recent_conversation) > 6:
                recent_conversation = recent_conversation[-6:]

            # Add recent conversation for context
            for msg in recent_conversation:
                # Only include clean user and assistant messages (no tool execution details)
                if msg["role"] == "user":
                    context.append(msg)
                elif msg["role"] == "assistant":
                    # Only include the final text response, not tool executions
                    text_content = ""
                    for content_item in msg.get("content", []):
                        if "text" in content_item:
                            text_content += content_item["text"]

                    if text_content.strip():
                        context.append({
                            "role": "assistant",
                            "content": [{"text": text_content}]
                        })

        # Add the current user message
        context.append({
            "role": "user",
            "content": [{"text": current_message}]
        })

        return context

    async def _build_aws_conversation_context(self, session_id: str, current_message: str) -> List[Dict]:
        """Build conversation context with AWS session awareness"""

        # Start with AWS context-aware system prompt
        system_prompt = await self._get_aws_context_aware_system_prompt(session_id)
        context = [{
            "role": "user",
            "content": [{"text": system_prompt}]
        }]

        # Try to get conversation history from session or fallback to legacy
        try:
            session_data = await self.session_manager.get_session(session_id)
            if session_data and "invocations" in session_data:
                # Build context from session invocations
                invocations = session_data["invocations"][-6:]  # Last 6 interactions

                for invocation in invocations:
                    step_data = invocation.get("stepData", {})
                    if step_data.get("role") == "user":
                        context.append({
                            "role": "user",
                            "content": [{"text": step_data.get("content", "")}]
                        })
                    elif step_data.get("role") == "assistant":
                        context.append({
                            "role": "assistant",
                            "content": [{"text": step_data.get("content", "")}]
                        })
            else:
                # Fallback to legacy conversation storage
                if session_id in self.conversations:
                    conversation = self.conversations[session_id]
                    if len(conversation) > 1:
                        recent_conversation = conversation[1:][-6:]  # Last 6 messages

                        for msg in recent_conversation:
                            if msg["role"] == "user":
                                context.append(msg)
                            elif msg["role"] == "assistant":
                                text_content = ""
                                for content_item in msg.get("content", []):
                                    if "text" in content_item:
                                        text_content += content_item["text"]

                                if text_content.strip():
                                    context.append({
                                        "role": "assistant",
                                        "content": [{"text": text_content}]
                                    })
        except Exception as e:
            logger.warning(f"Failed to build AWS conversation context: {e}")
            # Fallback to legacy method
            if session_id in self.conversations:
                return self._build_conversation_context(self.conversations[session_id], current_message)

        # Add the current user message
        context.append({
            "role": "user",
            "content": [{"text": current_message}]
        })

        return context

    async def execute_tool_internal(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Internal tool execution for automatic tool handling"""
        try:
            if tool_name not in self.tool_to_server:
                raise ValueError(f"Tool {tool_name} not found")

            server_name = self.tool_to_server[tool_name]
            server = self.servers[server_name]

            result = await server.execute_tool(tool_name, arguments)
            return result

        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            raise

    async def execute_aws_tool_with_context(self, session_id: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Execute AWS tool with session context enhancement"""
        try:
            # Get session context
            session_data = await self.session_manager.get_session(session_id)

            if session_data:
                session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})

                # Enhance arguments with AWS context
                enhanced_args = {
                    **arguments,
                    'aws_region': session_attributes.get('awsRegion', self.config.region),
                    'aws_profile': session_attributes.get('awsProfile', self.config.aws_profile),
                    'session_context': {
                        'active_services': session_attributes.get('activeServices', []),
                        'cost_context': session_attributes.get('costOptimizationContext', {}),
                        'budget_context': session_attributes.get('budgetContext', {})
                    }
                }
            else:
                enhanced_args = arguments

            # Execute tool
            result = await self.execute_tool_internal(tool_name, enhanced_args)

            # Update session with tool execution results
            await self._update_session_after_tool_execution(session_id, tool_name, result)

            return result

        except Exception as e:
            logger.error(f"Error executing AWS tool {tool_name}: {e}")
            raise

    async def _update_session_after_tool_execution(self, session_id: str, tool_name: str, result: Any):
        """Update session state based on tool execution results"""
        try:
            session_data = await self.session_manager.get_session(session_id)
            if not session_data:
                return

            current_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})
            updated_attributes = current_attributes.copy()

            # Update based on tool type
            if 'pricing' in tool_name.lower():
                updated_attributes['lastPricingQuery'] = {
                    'tool': tool_name,
                    'timestamp': datetime.now().isoformat(),
                    'result_summary': str(result)[:200]  # Store summary
                }

            if 'cost' in tool_name.lower():
                # Update cost optimization context
                cost_context = updated_attributes.get('costOptimizationContext', {})
                cost_context['lastAnalysis'] = {
                    'tool': tool_name,
                    'timestamp': datetime.now().isoformat()
                }
                updated_attributes['costOptimizationContext'] = cost_context

            # Track active AWS services
            active_services = set(updated_attributes.get('activeServices', []))
            if 'ec2' in tool_name.lower():
                active_services.add('EC2')
            elif 's3' in tool_name.lower():
                active_services.add('S3')
            elif 'rds' in tool_name.lower():
                active_services.add('RDS')
            elif 'lambda' in tool_name.lower():
                active_services.add('Lambda')
            elif 'pricing' in tool_name.lower():
                active_services.add('Pricing')
            elif 'cost' in tool_name.lower():
                active_services.add('Cost Explorer')

            updated_attributes['activeServices'] = list(active_services)

            # Update session
            await self.session_manager.update_session(session_id, {
                "sessionAttributes": updated_attributes
            })

        except Exception as e:
            logger.warning(f"Failed to update session after tool execution: {e}")

    async def get_response(self, message: str, session_id: str) -> ChatResponse:
        """Get complete chat response with enhanced AWS session management"""
        start_time = time.time()

        try:
            # Get or create AWS session
            session_data = await self.get_or_create_session(session_id)

            # Create checkpoint for user message
            await self.session_manager.create_invocation_checkpoint(session_id, {
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat(),
                "aws_context": {
                    "region": self.config.region,
                    "profile": self.config.aws_profile
                }
            })

            # Build AWS-aware conversation context
            conversation = await self._build_aws_conversation_context(session_id, message)

            # Update session metadata
            if session_data.get("fallback"):
                # Using fallback storage
                if session_id not in self.session_metadata:
                    self.session_metadata[session_id] = {
                        "created_at": datetime.now().isoformat(),
                        "message_count": 0
                    }
                self.session_metadata[session_id]["message_count"] += 1
                self.session_metadata[session_id]["last_activity"] = datetime.now().isoformat()
            else:
                # Update Bedrock session
                await self.update_session_context(session_id, {
                    "messageCount": session_data.get('sessionState', {}).get('sessionAttributes', {}).get('messageCount', 0) + 1,
                    "lastActivity": datetime.now().isoformat()
                })

            # Convert tools to Bedrock format
            bedrock_tools = [tool.to_bedrock_format() for tool in self.tools]

            # Get complete response with AWS-aware tool execution
            response_text, tool_executions, usage_info = await self.bedrock.converse_with_tools(
                conversation,
                bedrock_tools,
                lambda tool, args: self.execute_aws_tool_with_context(session_id, tool, args),
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )

            # Create checkpoint for assistant response
            await self.session_manager.create_invocation_checkpoint(session_id, {
                "role": "assistant",
                "content": response_text,
                "tool_executions": tool_executions,
                "aws_services_accessed": self._extract_aws_services_from_tools(tool_executions),
                "cost_analysis_performed": self._check_cost_analysis(tool_executions),
                "usage_info": usage_info,
                "timestamp": datetime.now().isoformat()
            })

            # Store in legacy format for backward compatibility
            if session_id not in self.conversations:
                self.conversations[session_id] = []

            self.conversations[session_id].append({
                "role": "user",
                "content": [{"text": message}]
            })

            self.conversations[session_id].append({
                "role": "assistant",
                "content": [{"text": response_text}]
            })

            processing_time = time.time() - start_time

            return ChatResponse(
                response=response_text,
                session_id=session_id,
                tool_executions=tool_executions,
                timestamp=datetime.now().isoformat(),
                tokens_used=usage_info.get('totalTokens'),
                processing_time=processing_time
            )

        except Exception as e:
            logger.error(f"Error in enhanced get_response: {e}")

            # Store error in session for debugging
            try:
                await self.session_manager.create_invocation_checkpoint(session_id, {
                    "role": "error",
                    "content": str(e),
                    "timestamp": datetime.now().isoformat()
                })
            except:
                pass  # Don't fail on error logging

            processing_time = time.time() - start_time

            return ChatResponse(
                response=f"I apologize, but I encountered an error while processing your AWS request: {str(e)}",
                session_id=session_id,
                tool_executions=[],
                timestamp=datetime.now().isoformat(),
                processing_time=processing_time
            )

    async def cleanup(self):
        """Clean up all resources"""
        logger.info("Starting chat session cleanup")
        cleanup_tasks = []
        for server in self.servers.values():
            cleanup_tasks.append(server.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        logger.info("Chat session cleanup completed")

# ------------------------
# API Endpoints
# ------------------------
# Event handlers moved to lifespan manager above

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy" if is_initialized else "initializing",
        "initialized": is_initialized,
        "available_tools": len(chat_session.tools) if chat_session and chat_session.tools else 0,
        "active_servers": len([s for s in chat_session.servers.values() if s._initialized]) if chat_session else 0,
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Chat endpoint with complete response after tool execution"""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="Message cannot be empty")
    
    session_id = request.session_id or str(uuid.uuid4())
    
    try:
        response = await chat_session.get_response(request.message, session_id)
        return response
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(
            status_code=500, 
            detail=ErrorResponse(
                error="Internal server error",
                details=str(e),
                timestamp=datetime.now().isoformat()
            ).dict()
        )

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session with AWS context"""
    global chat_session

    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")

    try:
        # Try to get from Bedrock session first
        session_data = await chat_session.session_manager.get_session(session_id)

        if session_data:
            # Extract conversation from session invocations
            messages = []
            invocations = session_data.get("invocations", [])

            for invocation in invocations:
                step_data = invocation.get("stepData", {})
                if step_data.get("role") in ["user", "assistant"]:
                    messages.append({
                        "role": step_data.get("role"),
                        "content": step_data.get("content", ""),
                        "timestamp": step_data.get("timestamp"),
                        "tool_executions": step_data.get("tool_executions", []) if step_data.get("role") == "assistant" else None
                    })

            return {
                "session_id": session_id,
                "messages": messages,
                "session_attributes": session_data.get('sessionState', {}).get('sessionAttributes', {}),
                "source": "bedrock_session"
            }

        # Fallback to legacy storage
        elif session_id in chat_session.conversations:
            return {
                "session_id": session_id,
                "messages": chat_session.conversations[session_id],
                "metadata": chat_session.session_metadata.get(session_id, {}),
                "source": "legacy_storage"
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")

    except Exception as e:
        logger.error(f"Error getting session history: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving session history: {str(e)}")

@app.get("/sessions/{session_id}/aws-context")
async def get_session_aws_context(session_id: str):
    """Get AWS-specific context for a session"""
    global chat_session

    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")

    try:
        session_data = await chat_session.session_manager.get_session(session_id)

        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")

        session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})

        return {
            "session_id": session_id,
            "aws_region": session_attributes.get('awsRegion'),
            "aws_profile": session_attributes.get('awsProfile'),
            "active_services": session_attributes.get('activeServices', []),
            "cost_optimization_context": session_attributes.get('costOptimizationContext', {}),
            "last_pricing_query": session_attributes.get('lastPricingQuery'),
            "budget_context": session_attributes.get('budgetContext', {}),
            "message_count": session_attributes.get('messageCount', 0),
            "created_at": session_attributes.get('createdAt'),
            "last_activity": session_attributes.get('lastActivity')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting AWS context: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving AWS context: {str(e)}")

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a conversation session from both Bedrock and legacy storage"""
    global chat_session

    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")

    try:
        # Delete from Bedrock session storage
        bedrock_deleted = await chat_session.session_manager.delete_session(session_id)

        # Delete from legacy storage
        legacy_deleted = False
        if session_id in chat_session.conversations:
            del chat_session.conversations[session_id]
            legacy_deleted = True

        if session_id in chat_session.session_metadata:
            del chat_session.session_metadata[session_id]
            legacy_deleted = True

        if bedrock_deleted or legacy_deleted:
            return {
                "message": f"Session {session_id} deleted",
                "bedrock_deleted": bedrock_deleted,
                "legacy_deleted": legacy_deleted
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")

@app.get("/tools")
async def list_available_tools():
    """List all available tools"""
    global chat_session
    
    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    tools_info = []
    for tool in chat_session.tools:
        tools_info.append({
            "name": tool.name,
            "description": tool.description,
            "server": tool.server_name,
            "input_schema": tool.input_schema
        })
    
    return {
        "tools": tools_info,
        "total_tools": len(tools_info),
        "servers": list(chat_session.servers.keys())
    }

# ------------------------
# Main Application Entry Point
# ------------------------
if __name__ == "__main__":
    # Ensure environment is set up
    load_dotenv()
    
    # Configuration validation
    required_env_vars = ["BEDROCK_MODEL_ID"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        exit(1)
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", "8000")),
        reload=os.getenv("ENV", "production") == "development",
        log_level=os.getenv("LOG_LEVEL", "info").lower(),
        workers=1  # Single worker for MCP connections
    )