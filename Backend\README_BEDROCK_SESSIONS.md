# AWS Bedrock Session Management Integration

This document describes the AWS Bedrock session management integration for enhanced context and session persistence in the MCP bot.

## 🎯 Overview

The integration adds persistent session management using AWS Bedrock's session APIs, providing:

- **Persistent Sessions**: Conversations survive server restarts
- **AWS Context Awareness**: Automatic tracking of AWS services and cost optimization context
- **Enhanced Tool Execution**: Tools receive rich session context for better responses
- **Graceful Fallback**: Automatic fallback to in-memory storage if Bedrock is unavailable
- **Backward Compatibility**: Existing functionality remains unchanged

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# AWS Bedrock Session Management
ENABLE_BEDROCK_SESSIONS=true          # Enable/disable Bedrock sessions
BEDROCK_SESSION_FALLBACK=true         # Enable fallback to memory storage
SESSION_RETENTION_DAYS=7              # Session retention period

# Existing AWS Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
AWS_REGION=ap-south-1
AWS_PROFILE=genai
```

### AWS Permissions

Ensure your AWS profile has these permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock-agent-runtime:CreateSession",
                "bedrock-agent-runtime:GetSession",
                "bedrock-agent-runtime:UpdateSession",
                "bedrock-agent-runtime:DeleteSession",
                "bedrock-agent-runtime:CreateInvocation",
                "bedrock-agent-runtime:PutInvocationStep",
                "bedrock-agent-runtime:ListInvocations",
                "bedrock-agent-runtime:ListInvocationSteps"
            ],
            "Resource": "*"
        }
    ]
}
```

## 🚀 Quick Start

1. **Update Configuration**:
   ```bash
   cp Backend/.env.example Backend/.env
   # Edit .env with your AWS settings
   ```

2. **Test the Integration**:
   ```bash
   python Backend/test_bedrock_sessions.py
   ```

3. **Start the Server**:
   ```bash
   python Backend/main.py
   ```

4. **Run the Demo**:
   ```bash
   python Backend/demo_enhanced_sessions.py
   ```

## 📋 New Features

### Enhanced Session Management

- **Persistent Storage**: Sessions are stored in AWS Bedrock and survive server restarts
- **AWS Context Tracking**: Automatic tracking of:
  - Active AWS services (EC2, S3, RDS, etc.)
  - Cost optimization context
  - Pricing query history
  - Budget context
  - Regional preferences

### Enhanced API Endpoints

#### `POST /chat`
- Now includes session persistence and AWS context awareness
- Tools receive enhanced context from session state
- Automatic checkpointing of conversations

#### `GET /sessions/{id}/aws-context`
- **NEW**: Get AWS-specific context for a session
- Returns active services, cost context, pricing history

#### `GET /sessions/{id}/history`
- Enhanced to work with both Bedrock sessions and legacy storage
- Includes session source information

#### `DELETE /sessions/{id}`
- Enhanced to clean up both Bedrock and legacy storage
- Returns deletion status for both storage types

## 🔄 Migration Strategy

The integration maintains full backward compatibility:

1. **Gradual Rollout**: Enable/disable via `ENABLE_BEDROCK_SESSIONS`
2. **Automatic Fallback**: Falls back to memory storage if Bedrock unavailable
3. **Dual Storage**: Maintains both Bedrock and legacy storage during transition
4. **Zero Downtime**: Existing sessions continue to work

## 🧪 Testing

### Unit Tests
```bash
python Backend/test_bedrock_sessions.py
```

### Integration Demo
```bash
python Backend/demo_enhanced_sessions.py
```

### Manual Testing
1. Start server: `python Backend/main.py`
2. Send chat messages with same session ID
3. Restart server
4. Continue conversation - context should be preserved

## 🛠️ Architecture

### BedrockSessionManager
- Handles all Bedrock session operations
- Provides fallback to in-memory storage
- Manages session lifecycle and cleanup

### Enhanced ChatSession
- Integrates with BedrockSessionManager
- Provides AWS context-aware conversation building
- Enhanced tool execution with session context

### Session State Structure
```json
{
  "sessionAttributes": {
    "awsRegion": "ap-south-1",
    "awsProfile": "genai",
    "createdAt": "2024-01-01T00:00:00Z",
    "messageCount": 5,
    "activeServices": ["EC2", "S3"],
    "costOptimizationContext": {},
    "lastPricingQuery": {
      "tool": "pricing-tool",
      "timestamp": "2024-01-01T00:00:00Z"
    },
    "budgetContext": {}
  }
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Bedrock Sessions Not Working**:
   - Check AWS credentials and permissions
   - Verify region supports Bedrock sessions
   - Check logs for specific error messages

2. **Fallback Storage Active**:
   - Normal behavior if Bedrock unavailable
   - Sessions will be memory-only until Bedrock is available

3. **Session Not Found**:
   - Check if session was created with Bedrock enabled
   - Verify session ID format and existence

### Debug Mode
Set `LOG_LEVEL=debug` in `.env` for detailed logging.

## 📊 Benefits

### For Users
- **Seamless Experience**: Conversations continue across server restarts
- **Better Context**: AI has access to previous AWS interactions
- **Improved Recommendations**: Context-aware cost optimization advice

### For Developers
- **Scalability**: Distributed session storage
- **Reliability**: Automatic fallback mechanisms
- **Observability**: Complete audit trail of interactions
- **Flexibility**: Easy to enable/disable features

## 🔮 Future Enhancements

- **Cross-Session Analytics**: Analyze patterns across multiple sessions
- **Advanced Context**: ML-powered context extraction
- **Multi-User Support**: User-specific session isolation
- **Session Sharing**: Collaborative session management

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review logs with debug mode enabled
3. Test with fallback mode (`ENABLE_BEDROCK_SESSIONS=false`)
4. Verify AWS permissions and configuration
