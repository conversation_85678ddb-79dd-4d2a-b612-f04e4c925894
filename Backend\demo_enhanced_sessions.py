#!/usr/bin/env python3
"""
Demo script showing enhanced AWS Bedrock session management capabilities
"""

import asyncio
import json
import requests
import time
import uuid
from datetime import datetime

# Demo configuration
BASE_URL = "http://localhost:8000"
DEMO_SESSION_ID = f"demo-session-{uuid.uuid4()}"

def print_section(title):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_response(response_data, title="Response"):
    """Print formatted response data"""
    print(f"\n📋 {title}:")
    print(json.dumps(response_data, indent=2))

async def demo_enhanced_chat():
    """Demonstrate enhanced chat with session persistence"""
    print_section("Enhanced Chat with AWS Session Management")
    
    # Test messages that will trigger different AWS tools
    test_messages = [
        "What are the current EC2 pricing options for t3.medium instances in ap-south-1?",
        "How much would it cost to run 10 t3.medium instances for a month?",
        "What are some cost optimization strategies for EC2 instances?",
        "Can you analyze the cost difference between on-demand and reserved instances?"
    ]
    
    print(f"🔄 Starting demo with session ID: {DEMO_SESSION_ID}")
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n💬 Message {i}: {message}")
        
        try:
            response = requests.post(f"{BASE_URL}/chat", json={
                "message": message,
                "session_id": DEMO_SESSION_ID
            })
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Response received (Processing time: {data.get('processing_time', 0):.2f}s)")
                print(f"🔧 Tools executed: {len(data.get('tool_executions', []))}")
                
                # Show tool executions
                for tool_exec in data.get('tool_executions', []):
                    print(f"   - {tool_exec.get('tool_name')}: {tool_exec.get('status')}")
                
                # Show response preview
                response_preview = data.get('response', '')[:200] + "..." if len(data.get('response', '')) > 200 else data.get('response', '')
                print(f"📝 Response preview: {response_preview}")
                
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
        
        # Small delay between messages
        time.sleep(2)

def demo_session_history():
    """Demonstrate session history retrieval"""
    print_section("Session History with AWS Context")
    
    try:
        # Get session history
        response = requests.get(f"{BASE_URL}/sessions/{DEMO_SESSION_ID}/history")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Session history retrieved")
            print(f"📊 Total messages: {len(data.get('messages', []))}")
            print(f"🔄 Source: {data.get('source', 'unknown')}")
            
            # Show message summary
            for i, msg in enumerate(data.get('messages', [])[:3], 1):  # Show first 3
                role = msg.get('role', 'unknown')
                content_preview = msg.get('content', '')[:100] + "..." if len(msg.get('content', '')) > 100 else msg.get('content', '')
                print(f"   {i}. {role}: {content_preview}")
            
            if len(data.get('messages', [])) > 3:
                print(f"   ... and {len(data.get('messages', [])) - 3} more messages")
                
        else:
            print(f"❌ Error getting history: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def demo_aws_context():
    """Demonstrate AWS context retrieval"""
    print_section("AWS Session Context")
    
    try:
        # Get AWS context
        response = requests.get(f"{BASE_URL}/sessions/{DEMO_SESSION_ID}/aws-context")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ AWS context retrieved")
            print_response(data, "AWS Session Context")
            
            # Highlight key information
            print(f"\n🎯 Key AWS Context:")
            print(f"   - Region: {data.get('aws_region')}")
            print(f"   - Active Services: {', '.join(data.get('active_services', []))}")
            print(f"   - Message Count: {data.get('message_count')}")
            print(f"   - Last Activity: {data.get('last_activity')}")
            
            if data.get('last_pricing_query'):
                print(f"   - Last Pricing Query: {data.get('last_pricing_query', {}).get('tool')}")
                
        else:
            print(f"❌ Error getting AWS context: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def demo_health_check():
    """Check service health and capabilities"""
    print_section("Service Health & Capabilities")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service is {data.get('status')}")
            print(f"🔧 Available tools: {data.get('available_tools')}")
            print(f"🖥️  Active servers: {data.get('active_servers')}")
            print(f"🔄 Initialized: {data.get('initialized')}")
            
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health check request failed: {e}")

def demo_session_cleanup():
    """Demonstrate session cleanup"""
    print_section("Session Cleanup")
    
    try:
        response = requests.delete(f"{BASE_URL}/sessions/{DEMO_SESSION_ID}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Session deleted successfully")
            print_response(data, "Deletion Result")
            
        else:
            print(f"❌ Error deleting session: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Delete request failed: {e}")

def print_demo_summary():
    """Print demo summary"""
    print_section("Demo Summary")
    
    print("🎉 AWS Bedrock Session Management Demo Complete!")
    print()
    print("✅ Demonstrated Features:")
    print("   - Enhanced chat with AWS session persistence")
    print("   - Automatic AWS context tracking")
    print("   - Tool execution with session awareness")
    print("   - Session history with AWS metadata")
    print("   - AWS-specific context retrieval")
    print("   - Graceful session cleanup")
    print()
    print("🔧 Key Benefits:")
    print("   - Conversations persist across server restarts")
    print("   - AWS context is maintained throughout sessions")
    print("   - Tool executions are enhanced with session context")
    print("   - Cost optimization context is tracked automatically")
    print("   - Fallback mechanisms ensure reliability")
    print()
    print("🚀 Ready for Production!")

async def main():
    """Main demo function"""
    print("🎯 AWS Bedrock Session Management Demo")
    print("=" * 60)
    print(f"🔗 Base URL: {BASE_URL}")
    print(f"🆔 Demo Session ID: {DEMO_SESSION_ID}")
    
    # Check if server is running
    try:
        requests.get(f"{BASE_URL}/health", timeout=5)
        print("✅ Server is running")
    except:
        print("❌ Server is not running. Please start with: python Backend/main.py")
        return 1
    
    # Run demo steps
    demo_health_check()
    await demo_enhanced_chat()
    demo_session_history()
    demo_aws_context()
    demo_session_cleanup()
    print_demo_summary()
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
