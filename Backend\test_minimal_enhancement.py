#!/usr/bin/env python3
"""
Minimal test for enhanced Bedrock session management
Tests the smallest possible changes to existing implementation
"""

import asyncio
import os
import sys
import uuid
from datetime import datetime
from dotenv import load_dotenv

# Add the Backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import Configuration, BedrockSessionManager

async def test_minimal_enhancement():
    """Test the minimal enhancement to existing session management"""
    print("🧪 Testing Minimal Bedrock Session Enhancement")
    print("=" * 60)
    
    # Load configuration
    load_dotenv()
    config = Configuration()
    
    print(f"✅ Configuration loaded:")
    print(f"   - Bedrock Sessions Enabled: {config.enable_bedrock_sessions}")
    print(f"   - AWS Region: {config.region}")
    print(f"   - AWS Profile: {config.aws_profile}")
    print()
    
    # Initialize session manager with minimal changes
    session_manager = BedrockSessionManager(config)
    print(f"✅ Session Manager initialized")
    print(f"   - Bedrock enabled: {session_manager.session_enabled}")
    print(f"   - Has bedrock_agent_runtime: {hasattr(session_manager, 'bedrock_agent_runtime')}")
    print()
    
    # Test session creation (this should work with both Bedrock and fallback)
    test_session_id = f"test-minimal-{uuid.uuid4()}"
    print(f"🔄 Creating test session: {test_session_id}")
    
    try:
        session_data = await session_manager.create_session(test_session_id, {
            "testMode": "minimal_enhancement",
            "userType": "demo"
        })
        
        print(f"✅ Session created successfully")
        print(f"   Session ID: {session_data.get('sessionId', 'N/A')}")
        
        # Check if it's using Bedrock or fallback
        if session_data.get('sessionId') and not session_data.get('fallback'):
            print(f"   ✅ Using Bedrock session management")
        else:
            print(f"   ⚠️  Using fallback storage (this is normal if Bedrock unavailable)")
        print()
        
        # Test session retrieval
        print(f"🔄 Testing session retrieval")
        retrieved_session = await session_manager.get_session(test_session_id)
        
        if retrieved_session:
            print(f"✅ Session retrieved successfully")
            session_attrs = retrieved_session.get('sessionState', {}).get('sessionAttributes', {})
            print(f"   AWS Region: {session_attrs.get('awsRegion')}")
            print(f"   Test Mode: {session_attrs.get('testMode')}")
            print(f"   Created At: {session_attrs.get('createdAt')}")
        else:
            print(f"❌ Failed to retrieve session")
            return False
        print()
        
        # Test session update
        print(f"🔄 Testing session update")
        await session_manager.update_session(test_session_id, {
            "sessionAttributes": {
                **session_attrs,
                "lastUpdate": datetime.now().isoformat(),
                "testStatus": "updated_successfully"
            }
        })
        print(f"✅ Session updated successfully")
        print()
        
        # Test cleanup
        print(f"🔄 Testing session cleanup")
        deleted = await session_manager.delete_session(test_session_id)
        print(f"✅ Session cleanup: {deleted}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        print(f"   This might be expected if Bedrock sessions are not available")
        print(f"   The system should gracefully fallback to in-memory storage")
        
        # Check if fallback is working
        if test_session_id in session_manager.fallback_sessions:
            print(f"✅ Fallback storage is working correctly")
            return True
        else:
            print(f"❌ Neither Bedrock nor fallback storage is working")
            return False

def test_configuration_options():
    """Test different configuration scenarios"""
    print("\n🧪 Testing Configuration Flexibility")
    print("=" * 60)
    
    # Test with Bedrock disabled
    print("🔄 Testing with Bedrock sessions disabled")
    os.environ["ENABLE_BEDROCK_SESSIONS"] = "false"
    config_disabled = Configuration()
    session_manager_disabled = BedrockSessionManager(config_disabled)
    
    print(f"✅ Bedrock disabled config:")
    print(f"   - Sessions Enabled: {session_manager_disabled.session_enabled}")
    print(f"   - Should use fallback storage only")
    
    # Reset environment
    os.environ["ENABLE_BEDROCK_SESSIONS"] = "true"
    print()
    return True

def print_enhancement_summary():
    """Print summary of the minimal enhancement"""
    print("\n📋 Minimal Enhancement Summary")
    print("=" * 60)
    print("✅ Changes Made:")
    print("   - Enhanced BedrockSessionManager initialization")
    print("   - Added connection testing for Bedrock")
    print("   - Improved session creation with fallback")
    print("   - Zero breaking changes to existing API")
    print()
    print("🔧 Benefits:")
    print("   - Better error handling and validation")
    print("   - Graceful fallback when Bedrock unavailable")
    print("   - Maintains all existing functionality")
    print("   - Easy to enable/disable via environment variables")
    print()
    print("🚀 Next Steps:")
    print("   1. Test with your existing chat functionality")
    print("   2. Monitor logs for any Bedrock connection issues")
    print("   3. Gradually enable for production traffic")
    print("   4. Consider adding LangGraph later if needed")

async def main():
    """Main test function"""
    print("🚀 Minimal Bedrock Session Enhancement Test")
    print("=" * 60)
    print()
    
    # Test minimal enhancement
    enhancement_test_passed = await test_minimal_enhancement()
    
    # Test configuration options
    config_test_passed = test_configuration_options()
    
    # Print summary
    print_enhancement_summary()
    
    if enhancement_test_passed and config_test_passed:
        print("\n🎉 All tests passed! Minimal enhancement is working correctly.")
        print("   Your existing chat functionality should work unchanged.")
        print("   Sessions will now persist across server restarts (if Bedrock available).")
        return 0
    else:
        print("\n⚠️  Some tests failed, but fallback mechanisms should ensure")
        print("   your existing functionality continues to work normally.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
