{"mcpServers": {"aws-pricing": {"command": "python", "args": ["-m", "awslabs.aws_pricing_mcp_server.server"], "env": {}, "cwd": "../mcp/src/aws-pricing-mcp-server", "description": "AWS Pricing API server for cost analysis"}, "cost-explorer": {"command": "python", "args": ["-m", "awslabs.cost_explorer_mcp_server.server"], "env": {}, "cwd": "../mcp/src/cost-explorer-mcp-server", "description": "AWS Cost Explorer API server for analyzing actual AWS spending, usage patterns, and cost trends"}}}