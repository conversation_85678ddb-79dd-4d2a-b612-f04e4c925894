# AWS Bedrock Configuration
BEDROCK_MODEL_ID=amazon.nova-lite-v1:0
AWS_REGION=ap-south-1
AWS_PROFILE=default

# Application Configuration - Token Optimized
MAX_CONVERSATION_LENGTH=15
MAX_CONCURRENT_TOOLS=5
USE_COMPRESSED_SCHEMAS=true

# API Configuration
API_BASE_URL=http://localhost:8000
API_TIMEOUT=120
ENABLE_THINKING_LOGS=false
PORT=8000

# Bedrock Session Management (Demo)
ENABLE_BEDROCK_SESSIONS=true
BEDROCK_SESSION_FALLBACK=true
SESSION_RETENTION_DAYS=7

# Application Configuration
MAX_TOKENS=4000
TEMPERATURE=0.1
PORT=8000
LOG_LEVEL=info
ENV=development
