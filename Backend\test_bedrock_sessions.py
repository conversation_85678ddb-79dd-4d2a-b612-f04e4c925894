#!/usr/bin/env python3
"""
Test script for AWS Bedrock session management integration
"""

import asyncio
import json
import os
import sys
import uuid
from datetime import datetime
from dotenv import load_dotenv

# Add the Backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import Configuration, BedrockSessionManager

async def test_bedrock_session_manager():
    """Test the BedrockSessionManager functionality"""
    print("🧪 Testing AWS Bedrock Session Manager Integration")
    print("=" * 60)
    
    # Load configuration
    load_dotenv()
    config = Configuration()
    
    print(f"✅ Configuration loaded:")
    print(f"   - Bedrock Sessions Enabled: {config.enable_bedrock_sessions}")
    print(f"   - Fallback Enabled: {config.bedrock_session_fallback}")
    print(f"   - AWS Region: {config.region}")
    print(f"   - AWS Profile: {config.aws_profile}")
    print()
    
    # Initialize session manager
    session_manager = BedrockSessionManager(config)
    print(f"✅ Session Manager initialized (Bedrock enabled: {session_manager.session_enabled})")
    print()
    
    # Test session creation
    test_session_id = f"test-session-{uuid.uuid4()}"
    print(f"🔄 Creating test session: {test_session_id}")
    
    try:
        session_data = await session_manager.create_session(test_session_id, {
            "testContext": "demo_session",
            "userPreferences": {"costOptimization": True}
        })
        print(f"✅ Session created successfully")
        print(f"   Session ID: {session_data.get('sessionId', 'N/A')}")
        print()
        
        # Test session retrieval
        print(f"🔄 Retrieving session: {test_session_id}")
        retrieved_session = await session_manager.get_session(test_session_id)
        
        if retrieved_session:
            print(f"✅ Session retrieved successfully")
            session_attrs = retrieved_session.get('sessionState', {}).get('sessionAttributes', {})
            print(f"   AWS Region: {session_attrs.get('awsRegion')}")
            print(f"   Created At: {session_attrs.get('createdAt')}")
            print(f"   Test Context: {session_attrs.get('testContext')}")
            print()
        else:
            print(f"❌ Failed to retrieve session")
            return False
        
        # Test session update
        print(f"🔄 Updating session context")
        await session_manager.update_session(test_session_id, {
            "sessionAttributes": {
                **session_attrs,
                "lastUpdate": datetime.now().isoformat(),
                "activeServices": ["EC2", "S3"],
                "messageCount": 5
            }
        })
        print(f"✅ Session updated successfully")
        print()
        
        # Test checkpoint creation
        print(f"🔄 Creating conversation checkpoint")
        invocation_id = await session_manager.create_invocation_checkpoint(test_session_id, {
            "role": "user",
            "content": "What are the current EC2 pricing options?",
            "timestamp": datetime.now().isoformat()
        })
        print(f"✅ Checkpoint created with invocation ID: {invocation_id}")
        print()
        
        # Test session deletion
        print(f"🔄 Deleting test session")
        deleted = await session_manager.delete_session(test_session_id)
        print(f"✅ Session deleted: {deleted}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        print(f"   This might be expected if Bedrock sessions are not available")
        print(f"   The system should fallback to in-memory storage")
        
        # Test fallback functionality
        print(f"\n🔄 Testing fallback storage")
        if test_session_id in session_manager.fallback_sessions:
            print(f"✅ Session found in fallback storage")
            fallback_data = session_manager.fallback_sessions[test_session_id]
            print(f"   Session data: {json.dumps(fallback_data, indent=2)}")
            return True
        else:
            print(f"❌ Session not found in fallback storage either")
            return False

async def test_configuration_scenarios():
    """Test different configuration scenarios"""
    print("\n🧪 Testing Configuration Scenarios")
    print("=" * 60)
    
    # Test with Bedrock disabled
    print("🔄 Testing with Bedrock sessions disabled")
    os.environ["ENABLE_BEDROCK_SESSIONS"] = "false"
    config_disabled = Configuration()
    session_manager_disabled = BedrockSessionManager(config_disabled)
    
    print(f"✅ Bedrock disabled config:")
    print(f"   - Sessions Enabled: {session_manager_disabled.session_enabled}")
    print(f"   - Should use fallback storage only")
    print()
    
    # Test session creation with disabled Bedrock
    test_session_id = f"fallback-test-{uuid.uuid4()}"
    session_data = await session_manager_disabled.create_session(test_session_id)
    
    if test_session_id in session_manager_disabled.fallback_sessions:
        print(f"✅ Fallback storage working correctly")
        print(f"   Session stored in memory: {test_session_id}")
    else:
        print(f"❌ Fallback storage not working")
        return False
    
    # Reset environment
    os.environ["ENABLE_BEDROCK_SESSIONS"] = "true"
    print()
    return True

def print_integration_summary():
    """Print summary of the integration"""
    print("\n📋 Integration Summary")
    print("=" * 60)
    print("✅ AWS Bedrock Session Management Features:")
    print("   - Persistent session storage across server restarts")
    print("   - AWS context-aware session attributes")
    print("   - Conversation checkpointing and recovery")
    print("   - Automatic fallback to in-memory storage")
    print("   - Enhanced tool execution with session context")
    print("   - Cost optimization context tracking")
    print("   - AWS service usage tracking")
    print()
    print("🔧 Configuration Options:")
    print("   - ENABLE_BEDROCK_SESSIONS: Enable/disable Bedrock sessions")
    print("   - BEDROCK_SESSION_FALLBACK: Enable fallback to memory storage")
    print("   - SESSION_RETENTION_DAYS: Session retention period")
    print()
    print("🚀 Ready for Demo!")
    print("   Start the server with: python Backend/main.py")
    print("   Test endpoints:")
    print("   - POST /chat - Enhanced chat with session persistence")
    print("   - GET /sessions/{id}/history - Get session history")
    print("   - GET /sessions/{id}/aws-context - Get AWS context")
    print("   - DELETE /sessions/{id} - Delete session")

async def main():
    """Main test function"""
    print("🚀 AWS Bedrock Session Management Integration Test")
    print("=" * 60)
    print()
    
    # Test session manager
    session_test_passed = await test_bedrock_session_manager()
    
    # Test configuration scenarios
    config_test_passed = await test_configuration_scenarios()
    
    # Print summary
    print_integration_summary()
    
    if session_test_passed and config_test_passed:
        print("\n🎉 All tests passed! Integration is ready for demo.")
        return 0
    else:
        print("\n⚠️  Some tests failed, but fallback mechanisms should work.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
